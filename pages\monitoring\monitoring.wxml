<!--系统监控仪表板-->
<view class="container">
  <!-- 头部状态 -->
  <view class="header">
    <view class="system-status {{systemStatus}}">
      <view class="status-icon">
        <text wx:if="{{systemStatus === 'healthy'}}">🟢</text>
        <text wx:elif="{{systemStatus === 'warning'}}">🟡</text>
        <text wx:elif="{{systemStatus === 'degraded'}}">🟠</text>
        <text wx:else>🔴</text>
      </view>
      <view class="status-info">
        <text class="status-title">系统状态</text>
        <text class="status-value">
          <text wx:if="{{systemStatus === 'healthy'}}">运行正常</text>
          <text wx:elif="{{systemStatus === 'warning'}}">轻微异常</text>
          <text wx:elif="{{systemStatus === 'degraded'}}">性能下降</text>
          <text wx:else>严重异常</text>
        </text>
      </view>
      <view class="health-score">
        <text class="score">{{systemHealth}}</text>
        <text class="unit">%</text>
      </view>
    </view>
    
    <view class="update-info">
      <text class="update-time">更新时间: {{lastUpdateTime}}</text>
      <button class="refresh-btn" bindtap="onRefresh" disabled="{{isRefreshing}}">
        <text class="{{isRefreshing ? 'rotating' : ''}}">🔄</text>
      </button>
    </view>
  </view>

  <!-- 性能指标 -->
  <view class="metrics-section">
    <view class="section-header">
      <text class="section-title">性能指标</text>
      <button class="detail-btn" bindtap="onViewPerformanceDetail">详情</button>
    </view>
    
    <view class="metrics-grid">
      <view class="metric-item">
        <view class="metric-icon">⚡</view>
        <view class="metric-info">
          <text class="metric-label">响应时间</text>
          <text class="metric-value">{{performanceMetrics.responseTime}}ms</text>
        </view>
        <view class="metric-status {{performanceMetrics.responseTime > 2000 ? 'warning' : 'normal'}}"></view>
      </view>
      
      <view class="metric-item">
        <view class="metric-icon">🧠</view>
        <view class="metric-info">
          <text class="metric-label">内存使用</text>
          <text class="metric-value">{{performanceMetrics.memoryUsage}}MB</text>
        </view>
        <view class="metric-status {{performanceMetrics.memoryUsage > 100 ? 'warning' : 'normal'}}"></view>
      </view>
      
      <view class="metric-item">
        <view class="metric-icon">👥</view>
        <view class="metric-info">
          <text class="metric-label">活跃用户</text>
          <text class="metric-value">{{performanceMetrics.activeUsers}}</text>
        </view>
        <view class="metric-status normal"></view>
      </view>
      
      <view class="metric-item">
        <view class="metric-icon">❌</view>
        <view class="metric-info">
          <text class="metric-label">错误率</text>
          <text class="metric-value">{{performanceMetrics.errorRate}}%</text>
        </view>
        <view class="metric-status {{performanceMetrics.errorRate > 5 ? 'warning' : 'normal'}}"></view>
      </view>
    </view>
  </view>

  <!-- 告警信息 -->
  <view class="alerts-section" wx:if="{{recentAlerts.length > 0}}">
    <view class="section-header">
      <text class="section-title">最近告警</text>
      <button class="clear-btn" bindtap="onClearAlerts">清理</button>
    </view>
    
    <view class="alerts-list">
      <view 
        class="alert-item {{alert.level}}"
        wx:for="{{recentAlerts}}" 
        wx:key="id"
        bindtap="onViewAlertDetail"
        data-alert="{{item}}"
      >
        <view class="alert-icon">
          <text wx:if="{{item.level === 'critical'}}">🚨</text>
          <text wx:elif="{{item.level === 'warning'}}">⚠️</text>
          <text wx:else>ℹ️</text>
        </view>
        <view class="alert-content">
          <text class="alert-message">{{item.details.message}}</text>
          <text class="alert-time">{{item.timestamp}}</text>
        </view>
        <view class="alert-arrow">></view>
      </view>
    </view>
  </view>

  <!-- 无告警状态 -->
  <view class="no-alerts" wx:if="{{recentAlerts.length === 0}}">
    <view class="no-alerts-icon">✅</view>
    <text class="no-alerts-text">暂无告警信息</text>
  </view>

  <!-- 趋势图表 -->
  <view class="charts-section">
    <view class="section-header">
      <text class="section-title">性能趋势</text>
    </view>
    
    <view class="chart-tabs">
      <view class="tab-item active" data-chart="responseTime">响应时间</view>
      <view class="tab-item" data-chart="memoryUsage">内存使用</view>
      <view class="tab-item" data-chart="errorRate">错误率</view>
    </view>
    
    <view class="chart-container">
      <view class="simple-chart">
        <view class="chart-placeholder">
          <text>📊 趋势图表</text>
          <text class="chart-note">数据点: {{chartData.responseTime.length}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="actions-section">
    <button class="action-btn primary" bindtap="onExportReport">
      📄 导出报告
    </button>
    <button class="action-btn secondary" bindtap="onRefresh">
      🔄 刷新数据
    </button>
  </view>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{isRefreshing}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在刷新数据...</text>
    </view>
  </view>
</view>

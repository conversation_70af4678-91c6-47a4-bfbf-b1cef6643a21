/* 系统监控仪表板样式 */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 头部状态 */
.header {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.system-status {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.status-icon {
  font-size: 60rpx;
}

.status-info {
  flex: 1;
}

.status-title {
  font-size: 28rpx;
  color: #666666;
  display: block;
  margin-bottom: 5rpx;
}

.status-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.system-status.healthy .status-value { color: #52c41a; }
.system-status.warning .status-value { color: #faad14; }
.system-status.degraded .status-value { color: #fa8c16; }
.system-status.critical .status-value { color: #f5222d; }

.health-score {
  text-align: center;
}

.score {
  font-size: 48rpx;
  font-weight: bold;
  color: #333333;
}

.unit {
  font-size: 24rpx;
  color: #666666;
}

.update-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.update-time {
  font-size: 24rpx;
  color: #999999;
}

.refresh-btn {
  background: none;
  border: none;
  padding: 0;
  font-size: 32rpx;
  color: #1890ff;
}

.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 性能指标 */
.metrics-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.detail-btn, .clear-btn {
  background: #f0f0f0;
  border: none;
  border-radius: 20rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
  color: #666666;
}

.metrics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 15rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 15rpx;
  position: relative;
}

.metric-icon {
  font-size: 40rpx;
}

.metric-info {
  flex: 1;
}

.metric-label {
  font-size: 24rpx;
  color: #666666;
  display: block;
  margin-bottom: 5rpx;
}

.metric-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
}

.metric-status {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  position: absolute;
  top: 15rpx;
  right: 15rpx;
}

.metric-status.normal { background-color: #52c41a; }
.metric-status.warning { background-color: #faad14; }

/* 告警信息 */
.alerts-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.alerts-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.alert-item {
  display: flex;
  align-items: center;
  gap: 15rpx;
  padding: 20rpx;
  border-radius: 15rpx;
  border-left: 6rpx solid;
}

.alert-item.critical {
  background: #fff2f0;
  border-left-color: #f5222d;
}

.alert-item.warning {
  background: #fffbe6;
  border-left-color: #faad14;
}

.alert-item.info {
  background: #f6ffed;
  border-left-color: #52c41a;
}

.alert-icon {
  font-size: 32rpx;
}

.alert-content {
  flex: 1;
}

.alert-message {
  font-size: 28rpx;
  color: #333333;
  display: block;
  margin-bottom: 5rpx;
}

.alert-time {
  font-size: 24rpx;
  color: #999999;
}

.alert-arrow {
  font-size: 24rpx;
  color: #cccccc;
}

/* 无告警状态 */
.no-alerts {
  text-align: center;
  padding: 60rpx 30rpx;
  background: white;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.no-alerts-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.no-alerts-text {
  font-size: 28rpx;
  color: #666666;
}

/* 趋势图表 */
.charts-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.chart-tabs {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.tab-item {
  padding: 15rpx 30rpx;
  border-radius: 25rpx;
  font-size: 26rpx;
  color: #666666;
  background: #f0f0f0;
}

.tab-item.active {
  background: #1890ff;
  color: white;
}

.chart-container {
  height: 300rpx;
  border-radius: 15rpx;
  overflow: hidden;
}

.simple-chart {
  height: 100%;
  background: linear-gradient(135deg, #f0f2f5 0%, #e6f7ff 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  text-align: center;
}

.chart-placeholder text:first-child {
  font-size: 48rpx;
  display: block;
  margin-bottom: 10rpx;
}

.chart-note {
  font-size: 24rpx;
  color: #666666;
}

/* 操作按钮 */
.actions-section {
  display: flex;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
}

.action-btn.primary {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  color: white;
}

.action-btn.secondary {
  background: white;
  color: #333333;
  border: 2rpx solid #d9d9d9;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  text-align: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f0f0f0;
  border-top: 6rpx solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666666;
}

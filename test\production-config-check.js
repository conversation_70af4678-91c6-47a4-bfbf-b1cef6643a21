/**
 * 生产环境配置验证脚本
 * 在微信开发者工具控制台执行
 */

class ProductionConfigChecker {
  constructor() {
    this.results = [];
    this.errors = [];
  }

  /**
   * 执行所有配置检查
   */
  async runAllChecks() {
    console.log('🔍 开始生产环境配置检查...');
    
    try {
      // 1. 检查小程序基础配置
      await this.checkMiniProgramConfig();
      
      // 2. 检查云开发环境
      await this.checkCloudEnvironment();
      
      // 3. 检查云函数部署状态
      await this.checkCloudFunctions();
      
      // 4. 检查数据库集合
      await this.checkDatabaseCollections();
      
      // 5. 检查AI API配置
      await this.checkAIAPIConfig();
      
      // 输出检查结果
      this.outputResults();
      
    } catch (error) {
      console.error('配置检查失败:', error);
      this.errors.push(error);
    }
  }

  /**
   * 检查小程序基础配置
   */
  async checkMiniProgramConfig() {
    console.log('📱 检查小程序基础配置...');
    
    try {
      const accountInfo = wx.getAccountInfoSync();
      const systemInfo = wx.getSystemInfoSync();
      
      this.addResult('小程序环境', {
        appId: accountInfo.miniProgram.appId,
        envVersion: accountInfo.miniProgram.envVersion,
        version: accountInfo.miniProgram.version,
        platform: systemInfo.platform
      });
      
      // 检查是否为生产环境
      if (accountInfo.miniProgram.envVersion !== 'release') {
        this.addWarning('当前不是生产环境', `当前环境: ${accountInfo.miniProgram.envVersion}`);
      }
      
    } catch (error) {
      this.addError('小程序配置检查失败', error);
    }
  }

  /**
   * 检查云开发环境
   */
  async checkCloudEnvironment() {
    console.log('☁️ 检查云开发环境...');
    
    try {
      // 测试云开发连通性
      const testResult = await wx.cloud.callFunction({
        name: 'getUserId',
        data: { test: true }
      });
      
      if (testResult.result) {
        this.addResult('云开发连通性', '✅ 正常');
      } else {
        this.addError('云开发连通性', '连接失败');
      }
      
    } catch (error) {
      this.addError('云开发环境检查失败', error);
    }
  }

  /**
   * 检查关键云函数部署状态
   */
  async checkCloudFunctions() {
    console.log('⚡ 检查云函数部署状态...');
    
    const criticalFunctions = [
      'login',
      'getUserId', 
      'callDoubaoAPI',
      'generateComment',
      'getStudents',
      'addStudent'
    ];
    
    for (const funcName of criticalFunctions) {
      try {
        const result = await wx.cloud.callFunction({
          name: funcName,
          data: { healthCheck: true }
        });
        
        this.addResult(`云函数 ${funcName}`, '✅ 部署正常');
        
      } catch (error) {
        this.addError(`云函数 ${funcName}`, `部署异常: ${error.message}`);
      }
    }
  }

  /**
   * 检查数据库集合
   */
  async checkDatabaseCollections() {
    console.log('🗄️ 检查数据库集合...');
    
    const requiredCollections = [
      'users',
      'students', 
      'records',
      'comments',
      'classes',
      'system_config',
      'user_consent'
    ];
    
    try {
      const db = wx.cloud.database();
      
      for (const collection of requiredCollections) {
        try {
          const result = await db.collection(collection).limit(1).get();
          this.addResult(`数据库集合 ${collection}`, '✅ 存在');
        } catch (error) {
          this.addError(`数据库集合 ${collection}`, `不存在或无权限: ${error.message}`);
        }
      }
      
    } catch (error) {
      this.addError('数据库检查失败', error);
    }
  }

  /**
   * 检查AI API配置
   */
  async checkAIAPIConfig() {
    console.log('🤖 检查AI API配置...');
    
    try {
      // 测试AI API调用
      const result = await wx.cloud.callFunction({
        name: 'callDoubaoAPI',
        data: {
          studentName: '测试学生',
          performanceMaterial: '测试材料',
          style: 'warm',
          length: 'short'
        }
      });
      
      if (result.result && result.result.success) {
        this.addResult('AI API配置', '✅ 正常');
      } else {
        this.addError('AI API配置', `调用失败: ${result.result?.message || '未知错误'}`);
      }
      
    } catch (error) {
      this.addError('AI API检查失败', error);
    }
  }

  /**
   * 添加检查结果
   */
  addResult(item, status) {
    this.results.push({ item, status, type: 'success' });
  }

  /**
   * 添加警告
   */
  addWarning(item, message) {
    this.results.push({ item, status: `⚠️ ${message}`, type: 'warning' });
  }

  /**
   * 添加错误
   */
  addError(item, error) {
    const message = error.message || error.toString();
    this.results.push({ item, status: `❌ ${message}`, type: 'error' });
    this.errors.push({ item, error });
  }

  /**
   * 输出检查结果
   */
  outputResults() {
    console.log('\n📊 配置检查结果汇总:');
    console.log('='.repeat(50));
    
    let successCount = 0;
    let warningCount = 0;
    let errorCount = 0;
    
    this.results.forEach(result => {
      console.log(`${result.item}: ${result.status}`);
      
      switch (result.type) {
        case 'success': successCount++; break;
        case 'warning': warningCount++; break;
        case 'error': errorCount++; break;
      }
    });
    
    console.log('='.repeat(50));
    console.log(`✅ 成功: ${successCount} | ⚠️ 警告: ${warningCount} | ❌ 错误: ${errorCount}`);
    
    // 判断是否可以上线
    if (errorCount === 0) {
      console.log('\n🎉 配置检查通过，可以继续后续测试！');
    } else {
      console.log('\n🚨 存在配置错误，必须修复后才能上线！');
      console.log('错误详情:');
      this.errors.forEach(error => {
        console.error(`- ${error.item}: ${error.error.message || error.error}`);
      });
    }
  }
}

// 使用方法：在微信开发者工具控制台执行
// const checker = new ProductionConfigChecker();
// checker.runAllChecks();

module.exports = { ProductionConfigChecker };

/**
 * 评语灵感君小程序应用入口
 * 基于微信云开发
 */
const { storeUtils } = require('./store/index');
const stateManager = require('./utils/stateManager');
const errorHandler = require('./utils/errorHandler');
const globalUtils = require('./utils/globalUtils');
const { databaseConnectionManager } = require('./utils/databaseConnectionManager');
const { realTimeSyncManager } = require('./utils/realTimeSyncManager');
const { systemMonitor } = require('./utils/systemMonitor');
const { getCurrentCloudConfig, initCloudDevelopment } = require('./config/cloudConfig');
const { cache, dataService } = require('./utils/cacheManager');
// cloudService将在云开发初始化完成后动态加载

App({
  /**
   * 全局数据
   */
  globalData: {
    userInfo: null,
    userInfoUpdateTime: 0, // 用户信息更新时间戳
    token: null,
    isLogin: false,
    cloudConfig: getCurrentCloudConfig(), // 统一云开发配置
    cloudEnv: getCurrentCloudConfig().env, // 云开发环境ID
    db: null, // 云数据库实例
    stateManager: stateManager, // 全局状态管理器
    errorHandler: errorHandler, // 全局错误处理器
    globalUtils: globalUtils, // 全局工具函数
    databaseConnectionManager: databaseConnectionManager, // 数据库连接管理器
    realTimeSyncManager: realTimeSyncManager, // 实时同步管理器
    systemMonitor: systemMonitor, // 系统监控器
    cloudService: null, // 云服务实例
    degradedMode: false, // 降级模式标识
    // 缓存管理器
    cache: cache,
    dataService: dataService
  },

  /**
   * 应用启动时触发
   */
  async onLaunch(options) {

    // 紧急修复模式：如果检测到环境问题，立即修复
    await this.emergencyFixIfNeeded();

    // 初始化云开发
    await this.initCloud();

    // 初始化生产监控（仅在生产环境）
    this.initProductionMonitoring();

    // 初始化应用
    this.initApp();
  },

  /**
   * 紧急修复检测
   */
  async emergencyFixIfNeeded() {
    try {

      // 快速测试云开发是否正常
      if (wx.cloud) {
        try {
          wx.cloud.init({ env: 'cloud1-4g85f8xlb8166ff1' });
          const db = wx.cloud.database();
          await db.collection('users').limit(1).get();

          return;
        } catch (error) {

        }
      }

      // 启动紧急修复
      const { emergencyFix } = require('./utils/emergencyFix');
      const fixResult = await emergencyFix.emergencyFix();

      if (fixResult.success) {

        wx.showToast({
          title: '环境修复成功',
          icon: 'success'
        });
      } else {
        console.error('[App] 紧急修复失败:', fixResult.message);
        this.globalData.degradedMode = true;
      }

    } catch (error) {
      console.error('[App] 紧急修复过程异常:', error);
      this.globalData.degradedMode = true;
    }
  },

  /**
   * 应用显示时触发
   */
  onShow(options) {

    // 执行生产环境数据清理
    this.performProductionCleanup();
  },

  /**
   * 应用隐藏时触发
   */
  onHide() {

  },

  /**
   * 执行生产环境数据清理
   */
  performProductionCleanup() {
    try {
      const { performFullCleanup } = require('./utils/dataCleanup');
      const { performProductionCheck } = require('./utils/productionDataValidator');

      // 执行数据清理（静默模式，不显示toast）
      performFullCleanup(null, { showToast: false });

      // 执行生产环境检查
      const checkResult = performProductionCheck();

      if (!checkResult.isClean) {

      }

    } catch (error) {
      console.error('❌ 生产环境数据清理失败:', error);
    }
  },

  /**
   * 应用发生错误时触发
   */
  onError(error) {
    console.error('评语灵感君小程序错误:', error);

    // 特殊处理常见错误
    if (error && error.message) {
      if (error.message.includes('__route__ is not defined')) {

        return;
      }
      if (error.message.includes('is not defined')) {

        return;
      }
      if (error.message.includes('module') && error.message.includes('is not defined')) {

        this.globalData.degradedMode = true;
        return;
      }
    }

    // 使用全局错误处理器
    if (this.globalData.errorHandler) {
      this.globalData.errorHandler.handleError(error);
    }
  },

  /**
   * 页面不存在时触发
   */
  onPageNotFound(res) {

    // 跳转到首页
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  /**
   * 初始化云开发 - 增强版，解决环境验证问题
   */
  async initCloud() {
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力');
      return;
    }

    try {

      // 强制清除可能的缓存
      if (wx.cloud._initialized) {

        delete wx.cloud._initialized;
      }

      // 使用强制配置初始化
      const cloudConfig = {
        env: 'cloud1-4g85f8xlb8166ff1', // 强制使用正确的环境ID
        traceUser: true,
        timeout: 60000
      };

      // 多次尝试初始化
      let initSuccess = false;
      for (let attempt = 1; attempt <= 3; attempt++) {
        try {

          wx.cloud.init(cloudConfig);

          const db = wx.cloud.database();
          await db.collection('users').limit(1).get();

          initSuccess = true;
          break;

        } catch (error) {
          console.error(`[App] 第${attempt}次初始化失败:`, error);
          if (attempt < 3) {

            await new Promise(resolve => setTimeout(resolve, 2000));
          }
        }
      }

      if (!initSuccess) {
        throw new Error('多次尝试初始化云开发均失败');
      }

      // 获取云数据库实例
      this.globalData.db = wx.cloud.database();
      this.globalData.cloudEnv = cloudConfig.env;

      // 云开发初始化完成后，再初始化cloudService
      await this.initCloudService();

    } catch (error) {
      console.error('[App] 云开发初始化失败:', error);

      // 降级处理
      this.handleCloudInitFailure(error);
    }
  },

  /**
   * 初始化云服务 - 增强版
   */
  async initCloudService() {
    try {

      // 初始化数据库连接管理器

      // 数据库连接管理器已在导入时自动初始化
      
      // 初始化云服务
      const { cloudService } = require('./services/cloudService');
      await cloudService.init();
      this.globalData.cloudService = cloudService;

      // 检查并初始化数据库集合
      await this.checkAndInitDatabase();

      // 初始化实时同步管理器

      await this.testSystemConnections();
      
      // 启动系统监控

      // 系统监控已在导入时自动初始化并启动
      
    } catch (error) {
      console.error('[App] 云服务初始化失败:', error);
      
      // 初始化失败时的降级处理
      this.handleInitializationFailure(error);
    }
  },

  /**
   * 处理云开发初始化失败
   */
  handleCloudInitFailure(error) {
    console.error('[App] 云开发初始化失败，启用降级模式:', error);

    this.globalData.degradedMode = true;

    // 显示用户友好的错误提示
    wx.showModal({
      title: '云开发连接异常',
      content: '检测到云开发环境连接问题，部分功能可能受限。是否尝试修复？',
      confirmText: '去修复',
      cancelText: '稍后再说',
      success: (res) => {
        if (res.confirm) {
          // 跳转到环境修复页面
          wx.navigateTo({
            url: '/pages/env-fix/index'
          }).catch(() => {
            // 如果页面不存在，显示修复指南
            wx.showModal({
              title: '修复指南',
              content: '请检查微信开发者工具中的云开发环境设置，确认环境ID是否正确。',
              showCancel: false
            });
          });
        }
      }
    });
  },

  /**
   * 获取系统状态报告
   */
  getSystemStatusReport() {
    try {
      return this.globalData.systemMonitor.getStatusReport();
    } catch (error) {
      console.error('[App] 获取系统状态报告失败:', error);
      return null;
    }
  },

  /**
   * 手动触发系统检查
   */
  async triggerSystemCheck() {
    try {

      return await this.globalData.systemMonitor.triggerManualCheck();
    } catch (error) {
      console.error('[App] 手动系统检查失败:', error);
      return null;
    }
  },

  /**
   * 检查并初始化数据库集合 - 增强版
   */
  async checkAndInitDatabase() {
    try {
      console.log('[App] 开始检查数据库集合...');

      // 使用重试机制调用initDatabase云函数
      const result = await this.callCloudFunctionWithRetry('initDatabase', {}, 3);

      if (result && result.code === 200) {
        console.log('[App] 数据库检查完成:', result.data);

        const { missingCollections, results } = result.data;
        if (missingCollections && missingCollections.length > 0) {
          console.log(`[App] 创建了 ${missingCollections.length} 个缺失的数据库集合:`, missingCollections);

          // 显示用户友好的提示
          wx.showToast({
            title: `初始化了${missingCollections.length}个数据库集合`,
            icon: 'success',
            duration: 2000
          });
        } else {
          console.log('[App] 所有必要的数据库集合都已存在');
        }

        // 记录初始化结果
        if (results && results.length > 0) {
          console.log('[App] 数据库初始化详情:', results);
        }

        return true;
      } else {
        console.warn('[App] 数据库检查返回异常结果:', result);
        return false;
      }
    } catch (error) {
      console.error('[App] 数据库初始化检查失败:', error);

      // 记录错误但不阻止应用启动
      this.recordInitializationError('database_init', error);

      // 显示用户友好的提示
      wx.showToast({
        title: '数据库初始化异常，部分功能可能受限',
        icon: 'none',
        duration: 3000
      });

      return false;
    }
  },

  /**
   * 带重试机制的云函数调用（应用级别）
   */
  async callCloudFunctionWithRetry(name, data = {}, maxRetries = 3) {
    let lastError;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`[App] 第${attempt}次尝试调用云函数 ${name}`);

        const res = await new Promise((resolve, reject) => {
          wx.cloud.callFunction({
            name,
            data,
            timeout: 30000, // 30秒超时
            success: resolve,
            fail: reject
          });
        });

        if (res.result) {
          console.log(`[App] 云函数 ${name} 调用成功`);
          return res.result;
        } else {
          throw new Error('云函数返回空结果');
        }
      } catch (error) {
        lastError = error;
        console.error(`[App] 第${attempt}次调用云函数 ${name} 失败:`, error);

        if (attempt < maxRetries) {
          // 指数退避重试
          const delay = Math.min(2000 * Math.pow(2, attempt - 1), 10000);
          console.log(`[App] ${delay}ms后重试...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError;
  },

  /**
   * 记录初始化错误
   */
  recordInitializationError(type, error) {
    try {
      const errorLog = {
        type,
        message: error.message || error.toString(),
        timestamp: new Date().toISOString(),
        stack: error.stack
      };

      let initErrors = wx.getStorageSync('init_errors') || [];
      initErrors.push(errorLog);

      // 只保留最近10条错误
      if (initErrors.length > 10) {
        initErrors = initErrors.slice(-10);
      }

      wx.setStorageSync('init_errors', initErrors);
    } catch (logError) {
      console.error('[App] 记录初始化错误失败:', logError);
    }
  },

  /**
   * 测试系统连接状态
   */
  async testSystemConnections() {
    try {

      const dbStats = this.globalData.databaseConnectionManager.getConnectionStats();

      if (this.globalData.cloudService) {
        await this.globalData.cloudService.testConnection();

      }

    } catch (error) {

      // 连接测试失败不影响应用启动
    }
  },

  /**
   * 处理初始化失败
   */
  handleInitializationFailure(error) {
    console.error('[App] 处理初始化失败:', error);
    
    // 记录错误到本地
    try {
      const errorLog = {
        timestamp: new Date().toISOString(),
        error: error.message || error.toString(),
        type: 'initialization_failure'
      };
      
      let errorLogs = wx.getStorageSync('error_logs') || [];
      errorLogs.push(errorLog);
      
      // 只保留最近50条错误日志
      if (errorLogs.length > 50) {
        errorLogs = errorLogs.slice(-50);
      }
      
      wx.setStorageSync('error_logs', errorLogs);
    } catch (logError) {
      console.error('[App] 记录错误日志失败:', logError);
    }
    
    // 设置降级模式标识
    this.globalData.degradedMode = true;
    
    // 显示用户友好的错误提示
    setTimeout(() => {
      wx.showToast({
        title: '服务初始化中，部分功能可能受限',
        icon: 'none',
        duration: 3000
      });
    }, 1000);
  },

  /**
   * 初始化应用
   */
  async initApp() {
    try {
      // 检查更新
      this.checkUpdate();

      // 获取系统信息
      this.getSystemInfo();

      // 初始化缓存系统
      await this.initCache();

      // 初始化用户信息
      await this.initUserInfo();

    } catch (error) {
      console.error('应用初始化失败:', error);
    }
  },

  /**
   * 初始化缓存系统
   */
  async initCache() {
    try {
      // 预热常用缓存
      const preloadKeys = ['user_info', 'statistics', 'ai_config'];
      await this.globalData.cache.warmup(preloadKeys);

      console.log('✅ 缓存系统初始化完成');
    } catch (error) {
      console.error('❌ 缓存系统初始化失败:', error);
    }
  },

  /**
   * 检查小程序更新
   */
  checkUpdate() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager();
      
      updateManager.onCheckForUpdate((res) => {
        if (res.hasUpdate) {

        }
      });
      
      updateManager.onUpdateReady(() => {
        wx.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否重启应用？',
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate();
            }
          }
        });
      });
      
      updateManager.onUpdateFailed(() => {
        console.error('新版本下载失败');
      });
    }
  },

  /**
   * 获取系统信息
   */
  getSystemInfo() {
    try {
      const systemInfo = {
        ...wx.getDeviceInfo(),
        ...wx.getWindowInfo(),
        ...wx.getAppBaseInfo()
      };
      this.globalData.systemInfo = systemInfo;

    } catch (error) {
      console.error('获取系统信息失败:', error);
    }
  },

  /**
   * 初始化用户信息
   */
  async initUserInfo() {
    try {
      // 检查登录状态（简化版本，直接从本地存储获取）
      const userInfo = wx.getStorageSync('userInfo');
      if (userInfo) {
        storeUtils.setUserInfo(userInfo);
        this.globalData.isLogin = true;
      }
    } catch (error) {
      console.error('初始化用户信息失败:', error);
    }
  },

  /**
   * 验证token有效性
   */
  async validateToken(token) {
    try {
      // 简化版本：直接从本地存储获取用户信息，避免云函数依赖
      const userInfo = wx.getStorageSync('userInfo');
      if (userInfo && token) {

        return userInfo;
      }

      return null;
    } catch (error) {
      console.error('验证token失败:', error);
      return null;
    }
  },

  /**
   * 获取用户信息
   */
  getUserInfo(callback) {
    const userInfo = storeUtils.getUserInfo();
    if (userInfo) {
      callback && callback(userInfo);
    } else {
      // 尝试从本地存储获取
      const localUserInfo = wx.getStorageSync('userInfo');
      if (localUserInfo) {
        storeUtils.setUserInfo(localUserInfo);
        callback && callback(localUserInfo);
      } else {
        callback && callback(null);
      }
    }
  },

  /**
   * 用户登录
   */
  async login() {
    try {
      // 获取微信登录code
      const loginRes = await new Promise((resolve, reject) => {
        wx.login({
          success: resolve,
          fail: reject
        });
      });

      if (loginRes.code) {
        // 调用云函数进行登录
        const result = await cloudService.login(loginRes.code);
        if (result.success) {
          const { userInfo, token } = result.data;

          // 保存token和用户信息
          wx.setStorageSync('token', token);
          wx.setStorageSync('userInfo', userInfo);
          storeUtils.setUserInfo(userInfo);

          return userInfo;
        } else {
          throw new Error(result.error || '登录失败');
        }
      } else {
        throw new Error('获取微信登录code失败');
      }
    } catch (error) {
      console.error('登录失败:', error);
      throw error;
    }
  },

  /**
   * 用户退出登录
   */
  logout() {
    // 清除本地存储
    wx.removeStorageSync('token');
    wx.removeStorageSync('userInfo');

    // 清除全局状态
    storeUtils.clear();

    // 跳转到首页
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  /**
   * 初始化生产监控
   */
  initProductionMonitoring() {
    try {
      // 检查是否为生产环境
      const accountInfo = wx.getAccountInfoSync();
      const isProduction = accountInfo.miniProgram.envVersion === 'release';

      if (isProduction) {
        console.log('🔍 启动生产环境监控');

        // 导入并启动生产监控
        const { productionMonitor } = require('./utils/productionMonitor');
        this.globalData.productionMonitor = productionMonitor;

        // 记录应用启动
        productionMonitor.recordUserActivity('app_launch');

        // 设置全局错误监听
        this.setupGlobalErrorMonitoring();

      } else {
        console.log('🧪 开发/体验环境，跳过生产监控');
      }

    } catch (error) {
      console.error('生产监控初始化失败:', error);
    }
  },

  /**
   * 设置全局错误监控
   */
  setupGlobalErrorMonitoring() {
    // 监听小程序错误
    wx.onError((error) => {
      if (this.globalData.productionMonitor) {
        this.globalData.productionMonitor.recordError({
          type: 'runtime_error',
          message: error,
          timestamp: Date.now()
        });
      }
    });

    // 监听未处理的Promise拒绝
    wx.onUnhandledRejection((res) => {
      if (this.globalData.productionMonitor) {
        this.globalData.productionMonitor.recordError({
          type: 'unhandled_rejection',
          message: res.reason,
          timestamp: Date.now()
        });
      }
    });
  },

});

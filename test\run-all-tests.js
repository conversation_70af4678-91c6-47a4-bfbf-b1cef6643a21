/**
 * 一键执行所有上线前测试
 * 在微信开发者工具控制台执行
 */

// 导入所有测试套件
const { ProductionConfigChecker } = require('./production-config-check.js');
const { SecurityTestRunner } = require('./run-security-tests.js');
const { BoundaryAndPerformanceTestRunner } = require('./run-boundary-tests.js');

/**
 * 主测试执行器
 */
class MasterTestRunner {
  constructor() {
    this.allResults = [];
    this.testPhases = [];
    this.startTime = Date.now();
    this.currentPhase = 0;
  }

  /**
   * 执行完整的上线前测试流程
   */
  async runCompleteTestSuite() {
    console.log('🚀 开始执行完整的上线前测试流程...');
    console.log('测试开始时间:', new Date().toLocaleString());
    console.log('='.repeat(80));
    
    try {
      // 阶段1: 生产环境配置检查
      await this.runPhase1_ConfigCheck();
      
      // 阶段2: 安全测试
      await this.runPhase2_SecurityTests();
      
      // 阶段3: 边界和性能测试
      await this.runPhase3_BoundaryAndPerformanceTests();
      
      // 阶段4: 生成最终报告
      this.generateFinalReport();
      
      // 阶段5: 给出上线建议
      this.generateLaunchRecommendation();
      
    } catch (error) {
      console.error('🚨 测试流程执行失败:', error);
      this.generateErrorReport(error);
    }
  }

  /**
   * 阶段1: 生产环境配置检查
   */
  async runPhase1_ConfigCheck() {
    console.log('\n📋 阶段1: 生产环境配置检查');
    console.log('-'.repeat(50));
    
    const phaseStartTime = Date.now();
    
    try {
      const configChecker = new ProductionConfigChecker();
      await configChecker.runAllChecks();
      
      const duration = Date.now() - phaseStartTime;
      const passed = configChecker.errors.length === 0;
      
      this.addPhaseResult('配置检查', passed, duration, {
        errors: configChecker.errors.length,
        warnings: configChecker.results.filter(r => r.type === 'warning').length,
        details: configChecker.results
      });
      
      if (!passed) {
        throw new Error(`配置检查失败，发现 ${configChecker.errors.length} 个错误`);
      }
      
      console.log('✅ 阶段1完成: 配置检查通过');
      
    } catch (error) {
      this.addPhaseResult('配置检查', false, Date.now() - phaseStartTime, { error: error.message });
      throw error;
    }
  }

  /**
   * 阶段2: 安全测试
   */
  async runPhase2_SecurityTests() {
    console.log('\n🔒 阶段2: 安全测试');
    console.log('-'.repeat(50));
    
    const phaseStartTime = Date.now();
    
    try {
      const securityRunner = new SecurityTestRunner();
      await securityRunner.runCompleteSecurityTests();
      
      const duration = Date.now() - phaseStartTime;
      const totalTests = securityRunner.results.length;
      const passedTests = securityRunner.results.filter(r => r.passed).length;
      const passRate = Math.round((passedTests / totalTests) * 100);
      
      this.addPhaseResult('安全测试', passRate >= 95, duration, {
        totalTests,
        passedTests,
        passRate,
        details: securityRunner.results
      });
      
      if (passRate < 95) {
        throw new Error(`安全测试通过率不足95% (当前: ${passRate}%)`);
      }
      
      console.log('✅ 阶段2完成: 安全测试通过');
      
    } catch (error) {
      this.addPhaseResult('安全测试', false, Date.now() - phaseStartTime, { error: error.message });
      throw error;
    }
  }

  /**
   * 阶段3: 边界和性能测试
   */
  async runPhase3_BoundaryAndPerformanceTests() {
    console.log('\n⚡ 阶段3: 边界和性能测试');
    console.log('-'.repeat(50));
    
    const phaseStartTime = Date.now();
    
    try {
      const boundaryRunner = new BoundaryAndPerformanceTestRunner();
      await boundaryRunner.runAllTests();
      
      const duration = Date.now() - phaseStartTime;
      const totalTests = boundaryRunner.results.length;
      const passedTests = boundaryRunner.results.filter(r => r.passed).length;
      const passRate = Math.round((passedTests / totalTests) * 100);
      
      this.addPhaseResult('边界和性能测试', passRate >= 90, duration, {
        totalTests,
        passedTests,
        passRate,
        performanceMetrics: boundaryRunner.performanceMetrics,
        details: boundaryRunner.results
      });
      
      if (passRate < 90) {
        console.warn(`⚠️ 边界和性能测试通过率: ${passRate}% (建议≥90%)`);
      }
      
      console.log('✅ 阶段3完成: 边界和性能测试完成');
      
    } catch (error) {
      this.addPhaseResult('边界和性能测试', false, Date.now() - phaseStartTime, { error: error.message });
      // 边界测试失败不阻断流程，但会影响最终评分
      console.warn('⚠️ 边界和性能测试出现问题，但继续执行');
    }
  }

  /**
   * 添加阶段结果
   */
  addPhaseResult(phaseName, passed, duration, details) {
    this.testPhases.push({
      phase: phaseName,
      passed,
      duration,
      details,
      timestamp: Date.now()
    });
  }

  /**
   * 生成最终报告
   */
  generateFinalReport() {
    const endTime = Date.now();
    const totalDuration = endTime - this.startTime;
    
    console.log('\n📊 上线前测试最终报告');
    console.log('='.repeat(80));
    console.log(`测试开始时间: ${new Date(this.startTime).toLocaleString()}`);
    console.log(`测试结束时间: ${new Date(endTime).toLocaleString()}`);
    console.log(`测试总耗时: ${Math.round(totalDuration / 1000)}秒`);
    console.log('='.repeat(80));
    
    // 各阶段结果汇总
    console.log('\n📋 各阶段测试结果:');
    this.testPhases.forEach((phase, index) => {
      const status = phase.passed ? '✅ 通过' : '❌ 失败';
      const duration = Math.round(phase.duration / 1000);
      console.log(`${index + 1}. ${phase.phase}: ${status} (耗时: ${duration}秒)`);
      
      if (phase.details.passRate !== undefined) {
        console.log(`   通过率: ${phase.details.passRate}%`);
      }
      
      if (phase.details.error) {
        console.log(`   错误: ${phase.details.error}`);
      }
    });
    
    // 性能指标汇总
    const performancePhase = this.testPhases.find(p => p.phase === '边界和性能测试');
    if (performancePhase && performancePhase.details.performanceMetrics) {
      console.log('\n⏱️ 关键性能指标:');
      performancePhase.details.performanceMetrics.forEach(metric => {
        const status = metric.passed ? '✅' : '❌';
        console.log(`${status} ${metric.name}: ${metric.avgTime}ms (阈值: ${metric.threshold}ms)`);
      });
    }
  }

  /**
   * 生成上线建议
   */
  generateLaunchRecommendation() {
    console.log('\n🎯 上线建议');
    console.log('='.repeat(80));
    
    const passedPhases = this.testPhases.filter(p => p.passed).length;
    const totalPhases = this.testPhases.length;
    const overallScore = Math.round((passedPhases / totalPhases) * 100);
    
    // 计算详细评分
    let detailedScore = 0;
    let maxScore = 0;
    
    this.testPhases.forEach(phase => {
      switch (phase.phase) {
        case '配置检查':
          maxScore += 30;
          if (phase.passed) detailedScore += 30;
          break;
        case '安全测试':
          maxScore += 40;
          if (phase.passed) {
            detailedScore += 40;
          } else if (phase.details.passRate >= 90) {
            detailedScore += 30; // 部分分数
          }
          break;
        case '边界和性能测试':
          maxScore += 30;
          if (phase.passed) {
            detailedScore += 30;
          } else if (phase.details.passRate >= 80) {
            detailedScore += 20; // 部分分数
          }
          break;
      }
    });
    
    const finalScore = Math.round((detailedScore / maxScore) * 100);
    
    console.log(`总体评分: ${finalScore}/100`);
    console.log(`阶段通过率: ${passedPhases}/${totalPhases}`);
    
    // 根据评分给出建议
    if (finalScore >= 95) {
      console.log('\n🎉 建议: 立即上线');
      console.log('✅ 所有关键测试都已通过');
      console.log('✅ 系统已准备好面对生产环境');
      console.log('✅ 可以开始提交小程序审核');
      
      this.printLaunchChecklist();
      
    } else if (finalScore >= 85) {
      console.log('\n⚠️ 建议: 修复问题后上线');
      console.log('⚠️ 大部分测试通过，但存在需要修复的问题');
      console.log('⚠️ 建议在1-2天内修复问题后上线');
      
      this.printIssuesList();
      
    } else if (finalScore >= 70) {
      console.log('\n🔧 建议: 重大修复后再考虑上线');
      console.log('❌ 存在重要问题需要修复');
      console.log('❌ 建议在1周内完成修复和重新测试');
      
      this.printIssuesList();
      this.printRiskWarnings();
      
    } else {
      console.log('\n🚨 建议: 暂停上线计划');
      console.log('🚨 系统存在严重问题，不适合上线');
      console.log('🚨 需要进行重大修复和架构调整');
      
      this.printIssuesList();
      this.printRiskWarnings();
      this.printEmergencyActions();
    }
  }

  /**
   * 打印上线检查清单
   */
  printLaunchChecklist() {
    console.log('\n📋 上线前最后检查清单:');
    console.log('□ 确认生产环境AppID配置正确');
    console.log('□ 确认云开发环境ID正确');
    console.log('□ 确认AI API密钥已配置');
    console.log('□ 备份当前版本代码');
    console.log('□ 准备回滚方案');
    console.log('□ 设置上线后监控');
  }

  /**
   * 打印问题列表
   */
  printIssuesList() {
    console.log('\n❌ 需要修复的问题:');
    
    this.testPhases.forEach(phase => {
      if (!phase.passed) {
        console.log(`- ${phase.phase}: ${phase.details.error || '测试未通过'}`);
        
        if (phase.details.details) {
          const failedTests = phase.details.details.filter(d => d.passed === false);
          failedTests.forEach(test => {
            console.log(`  • ${test.testName}: ${test.message}`);
          });
        }
      }
    });
  }

  /**
   * 打印风险警告
   */
  printRiskWarnings() {
    console.log('\n⚠️ 风险警告:');
    console.log('- 安全测试未完全通过可能导致数据泄露');
    console.log('- 性能测试未通过可能导致用户体验差');
    console.log('- 边界测试未通过可能导致系统在异常情况下崩溃');
  }

  /**
   * 打印紧急行动建议
   */
  printEmergencyActions() {
    console.log('\n🚨 紧急行动建议:');
    console.log('1. 立即停止所有上线准备工作');
    console.log('2. 组织技术团队分析失败原因');
    console.log('3. 制定详细的修复计划');
    console.log('4. 考虑架构重构或技术选型调整');
    console.log('5. 重新评估上线时间表');
  }

  /**
   * 生成错误报告
   */
  generateErrorReport(error) {
    console.log('\n🚨 测试流程异常终止');
    console.log('='.repeat(80));
    console.log('错误信息:', error.message);
    console.log('错误时间:', new Date().toLocaleString());
    
    if (this.testPhases.length > 0) {
      console.log('\n已完成的测试阶段:');
      this.testPhases.forEach((phase, index) => {
        const status = phase.passed ? '✅' : '❌';
        console.log(`${index + 1}. ${phase.phase}: ${status}`);
      });
    }
    
    console.log('\n建议:');
    console.log('1. 检查网络连接和云开发环境');
    console.log('2. 确认所有云函数已正确部署');
    console.log('3. 检查小程序权限配置');
    console.log('4. 重新运行测试');
  }
}

/**
 * 快速启动函数
 */
async function runAllPreLaunchTests() {
  const masterRunner = new MasterTestRunner();
  await masterRunner.runCompleteTestSuite();
}

// 使用方法：在微信开发者工具控制台执行
// runAllPreLaunchTests();

console.log('📋 上线前测试套件已加载');
console.log('执行命令: runAllPreLaunchTests()');

module.exports = { 
  MasterTestRunner, 
  runAllPreLaunchTests 
};

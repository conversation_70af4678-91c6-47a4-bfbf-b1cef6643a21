<!--测试执行页面-->
<view class="container">
  <view class="header">
    <text class="title">🚀 上线前测试</text>
    <text class="subtitle">确保系统稳定可靠</text>
  </view>

  <view class="test-controls">
    <button 
      class="test-btn primary" 
      bindtap="runAllTests" 
      disabled="{{isRunning}}"
    >
      {{isRunning ? '测试进行中...' : '执行完整测试'}}
    </button>
    
    <view class="quick-tests">
      <button 
        class="test-btn secondary" 
        bindtap="runConfigOnly" 
        disabled="{{isRunning}}"
      >
        配置检查
      </button>
      <button 
        class="test-btn secondary" 
        bindtap="runSecurityOnly" 
        disabled="{{isRunning}}"
      >
        安全测试
      </button>
    </view>
  </view>

  <view class="status-section" wx:if="{{currentTest}}">
    <view class="status-header">
      <text class="status-title">当前状态</text>
    </view>
    <view class="status-content">
      <text class="status-text">{{currentTest}}</text>
      <view class="loading" wx:if="{{isRunning}}">
        <view class="loading-dot"></view>
        <view class="loading-dot"></view>
        <view class="loading-dot"></view>
      </view>
    </view>
  </view>

  <view class="score-section" wx:if="{{overallScore > 0}}">
    <view class="score-header">
      <text class="score-title">总体评分</text>
    </view>
    <view class="score-content">
      <view class="score-circle {{overallScore >= 95 ? 'excellent' : overallScore >= 85 ? 'good' : overallScore >= 70 ? 'warning' : 'danger'}}">
        <text class="score-number">{{overallScore}}</text>
        <text class="score-unit">%</text>
      </view>
      <view class="score-status">
        <text wx:if="{{overallScore >= 95}}" class="status-excellent">🎉 可以上线</text>
        <text wx:elif="{{overallScore >= 85}}" class="status-good">⚠️ 修复后上线</text>
        <text wx:elif="{{overallScore >= 70}}" class="status-warning">🔧 需要重大修复</text>
        <text wx:else class="status-danger">🚨 暂停上线</text>
      </view>
    </view>
  </view>

  <view class="results-section" wx:if="{{testResults.length > 0}}">
    <view class="results-header">
      <text class="results-title">测试结果</text>
    </view>
    
    <view class="results-content">
      <view 
        class="result-category" 
        wx:for="{{testResults}}" 
        wx:key="category"
      >
        <view class="category-header">
          <text class="category-name">{{item.category}}</text>
          <text class="category-time">{{item.timestamp}}</text>
        </view>
        
        <view class="category-results">
          <view 
            class="result-item {{result.passed ? 'passed' : 'failed'}}"
            wx:for="{{item.results}}" 
            wx:for-item="result"
            wx:key="name"
          >
            <view class="result-icon">
              <text wx:if="{{result.passed}}">✅</text>
              <text wx:else>❌</text>
            </view>
            <view class="result-content">
              <text class="result-name">{{result.name}}</text>
              <text class="result-message">{{result.message}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <view class="tips-section">
    <view class="tips-header">
      <text class="tips-title">💡 使用说明</text>
    </view>
    <view class="tips-content">
      <text class="tip-item">• 执行完整测试需要2-3分钟</text>
      <text class="tip-item">• 确保网络连接正常</text>
      <text class="tip-item">• 测试过程中请勿关闭页面</text>
      <text class="tip-item">• 如有问题请查看控制台日志</text>
    </view>
  </view>
</view>

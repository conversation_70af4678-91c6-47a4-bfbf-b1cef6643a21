/* 测试执行页面样式 */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 40rpx 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  color: white;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}

.test-controls {
  margin-bottom: 40rpx;
}

.test-btn {
  width: 100%;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  margin-bottom: 20rpx;
}

.test-btn.primary {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.test-btn.primary:disabled {
  background: #cccccc;
  color: #999999;
}

.test-btn.secondary {
  background: white;
  color: #333333;
  border: 2rpx solid #e0e0e0;
}

.quick-tests {
  display: flex;
  gap: 20rpx;
}

.quick-tests .test-btn {
  flex: 1;
  font-size: 28rpx;
  height: 70rpx;
}

.status-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.status-header {
  margin-bottom: 20rpx;
}

.status-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.status-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.status-text {
  font-size: 28rpx;
  color: #666666;
}

.loading {
  display: flex;
  gap: 8rpx;
}

.loading-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: #4facfe;
  animation: loading 1.4s infinite ease-in-out both;
}

.loading-dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes loading {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.score-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.score-header {
  margin-bottom: 30rpx;
}

.score-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.score-content {
  display: flex;
  align-items: center;
  gap: 40rpx;
}

.score-circle {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

.score-circle.excellent {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.score-circle.good {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.score-circle.warning {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.score-circle.danger {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.score-number {
  font-size: 36rpx;
  font-weight: bold;
  color: white;
  line-height: 1;
}

.score-unit {
  font-size: 20rpx;
  color: white;
  opacity: 0.9;
}

.score-status {
  flex: 1;
}

.score-status text {
  font-size: 28rpx;
  font-weight: bold;
}

.status-excellent { color: #4facfe; }
.status-good { color: #43e97b; }
.status-warning { color: #fa709a; }
.status-danger { color: #ff6b6b; }

.results-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.results-header {
  margin-bottom: 30rpx;
}

.results-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.result-category {
  margin-bottom: 30rpx;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.category-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
}

.category-time {
  font-size: 24rpx;
  color: #999999;
}

.result-item {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.result-item:last-child {
  border-bottom: none;
}

.result-icon {
  font-size: 32rpx;
  line-height: 1;
}

.result-content {
  flex: 1;
}

.result-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  display: block;
  margin-bottom: 5rpx;
}

.result-message {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.4;
}

.result-item.passed .result-name {
  color: #43e97b;
}

.result-item.failed .result-name {
  color: #ff6b6b;
}

.tips-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.tips-header {
  margin-bottom: 20rpx;
}

.tips-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.tip-item {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.4;
}

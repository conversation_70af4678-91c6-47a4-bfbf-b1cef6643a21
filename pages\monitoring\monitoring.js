/**
 * 系统监控仪表板
 * 实时显示系统状态和性能指标
 */

const app = getApp();

Page({
  data: {
    // 系统状态
    systemHealth: 100,
    systemStatus: 'healthy',
    
    // 性能指标
    performanceMetrics: {
      responseTime: 0,
      memoryUsage: 0,
      activeUsers: 0,
      errorRate: 0
    },
    
    // 最近告警
    recentAlerts: [],
    
    // 监控数据
    monitoringData: null,
    
    // 刷新状态
    isRefreshing: false,
    lastUpdateTime: '',
    
    // 图表数据
    chartData: {
      responseTime: [],
      memoryUsage: [],
      errorRate: []
    }
  },

  onLoad() {
    console.log('监控仪表板加载');
    this.loadMonitoringData();
    this.startAutoRefresh();
  },

  onUnload() {
    this.stopAutoRefresh();
  },

  /**
   * 加载监控数据
   */
  async loadMonitoringData() {
    if (this.data.isRefreshing) return;
    
    this.setData({ isRefreshing: true });
    
    try {
      // 获取生产监控实例
      const productionMonitor = app.globalData.productionMonitor;
      
      if (!productionMonitor) {
        this.showNoMonitoringMessage();
        return;
      }
      
      // 获取监控报告
      const report = productionMonitor.getMonitoringReport();
      
      // 更新数据
      this.updateMonitoringData(report);
      
      // 更新时间
      this.setData({
        lastUpdateTime: new Date().toLocaleTimeString()
      });
      
    } catch (error) {
      console.error('加载监控数据失败:', error);
      wx.showToast({
        title: '数据加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ isRefreshing: false });
    }
  },

  /**
   * 更新监控数据
   */
  updateMonitoringData(report) {
    // 计算系统健康度
    const systemHealth = this.calculateSystemHealth(report);
    const systemStatus = this.getSystemStatus(systemHealth);
    
    // 提取性能指标
    const performanceMetrics = this.extractPerformanceMetrics(report);
    
    // 获取最近告警
    const recentAlerts = report.alerts.slice(-5); // 最近5条告警
    
    // 更新图表数据
    this.updateChartData(report);
    
    this.setData({
      systemHealth,
      systemStatus,
      performanceMetrics,
      recentAlerts,
      monitoringData: report
    });
  },

  /**
   * 计算系统健康度
   */
  calculateSystemHealth(report) {
    let health = 100;
    
    // 基于告警数量扣分
    const criticalAlerts = report.alerts.filter(a => a.level === 'critical').length;
    const warningAlerts = report.alerts.filter(a => a.level === 'warning').length;
    
    health -= criticalAlerts * 20; // 严重告警扣20分
    health -= warningAlerts * 5;   // 警告告警扣5分
    
    // 基于性能指标扣分
    if (report.summary.systemHealth) {
      health = Math.min(health, report.summary.systemHealth);
    }
    
    return Math.max(0, health);
  },

  /**
   * 获取系统状态
   */
  getSystemStatus(health) {
    if (health >= 90) return 'healthy';
    if (health >= 70) return 'warning';
    if (health >= 50) return 'degraded';
    return 'critical';
  },

  /**
   * 提取性能指标
   */
  extractPerformanceMetrics(report) {
    const metrics = {
      responseTime: 0,
      memoryUsage: 0,
      activeUsers: report.summary.activeUsers || 0,
      errorRate: 0
    };
    
    // 计算平均响应时间
    if (report.metrics.performance.length > 0) {
      const avgResponseTime = report.metrics.performance.reduce((sum, m) => sum + m.responseTime, 0) / report.metrics.performance.length;
      metrics.responseTime = Math.round(avgResponseTime);
    }
    
    // 计算平均内存使用
    if (report.metrics.performance.length > 0) {
      const avgMemory = report.metrics.performance.reduce((sum, m) => sum + m.memoryUsage, 0) / report.metrics.performance.length;
      metrics.memoryUsage = Math.round(avgMemory);
    }
    
    // 计算错误率
    if (report.metrics.errors.length > 0) {
      const avgErrorRate = report.metrics.errors.reduce((sum, m) => sum + m.errorRate, 0) / report.metrics.errors.length;
      metrics.errorRate = Math.round(avgErrorRate * 100);
    }
    
    return metrics;
  },

  /**
   * 更新图表数据
   */
  updateChartData(report) {
    const chartData = this.data.chartData;
    
    // 响应时间趋势
    if (report.metrics.performance.length > 0) {
      chartData.responseTime = report.metrics.performance.map(m => ({
        time: new Date(m.timestamp).toLocaleTimeString(),
        value: m.responseTime
      })).slice(-10); // 最近10个数据点
    }
    
    // 内存使用趋势
    if (report.metrics.performance.length > 0) {
      chartData.memoryUsage = report.metrics.performance.map(m => ({
        time: new Date(m.timestamp).toLocaleTimeString(),
        value: m.memoryUsage
      })).slice(-10);
    }
    
    // 错误率趋势
    if (report.metrics.errors.length > 0) {
      chartData.errorRate = report.metrics.errors.map(m => ({
        time: new Date(m.timestamp).toLocaleTimeString(),
        value: m.errorRate * 100
      })).slice(-10);
    }
    
    this.setData({ chartData });
  },

  /**
   * 显示无监控消息
   */
  showNoMonitoringMessage() {
    wx.showModal({
      title: '监控未启用',
      content: '当前环境未启用生产监控，请在生产环境中查看监控数据',
      showCancel: false
    });
  },

  /**
   * 开始自动刷新
   */
  startAutoRefresh() {
    this.refreshTimer = setInterval(() => {
      this.loadMonitoringData();
    }, 30000); // 每30秒刷新一次
  },

  /**
   * 停止自动刷新
   */
  stopAutoRefresh() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
      this.refreshTimer = null;
    }
  },

  /**
   * 手动刷新
   */
  onRefresh() {
    this.loadMonitoringData();
  },

  /**
   * 查看告警详情
   */
  onViewAlertDetail(e) {
    const alert = e.currentTarget.dataset.alert;
    
    wx.showModal({
      title: '告警详情',
      content: `类型: ${alert.type}\n级别: ${alert.level}\n时间: ${new Date(alert.timestamp).toLocaleString()}\n详情: ${alert.details.message}`,
      showCancel: false
    });
  },

  /**
   * 查看性能详情
   */
  onViewPerformanceDetail() {
    if (!this.data.monitoringData) return;
    
    const metrics = this.data.performanceMetrics;
    const content = `响应时间: ${metrics.responseTime}ms\n内存使用: ${metrics.memoryUsage}MB\n活跃用户: ${metrics.activeUsers}\n错误率: ${metrics.errorRate}%`;
    
    wx.showModal({
      title: '性能指标详情',
      content: content,
      showCancel: false
    });
  },

  /**
   * 导出监控报告
   */
  onExportReport() {
    if (!this.data.monitoringData) {
      wx.showToast({
        title: '暂无数据可导出',
        icon: 'none'
      });
      return;
    }
    
    try {
      // 生成报告内容
      const report = this.generateReportContent();
      
      // 保存到本地存储
      wx.setStorageSync('monitoring_report', {
        data: report,
        timestamp: Date.now()
      });
      
      wx.showToast({
        title: '报告已保存',
        icon: 'success'
      });
      
    } catch (error) {
      console.error('导出报告失败:', error);
      wx.showToast({
        title: '导出失败',
        icon: 'none'
      });
    }
  },

  /**
   * 生成报告内容
   */
  generateReportContent() {
    const data = this.data.monitoringData;
    const metrics = this.data.performanceMetrics;
    
    return {
      timestamp: Date.now(),
      systemHealth: this.data.systemHealth,
      systemStatus: this.data.systemStatus,
      performanceMetrics: metrics,
      alertsCount: {
        total: data.alerts.length,
        critical: data.alerts.filter(a => a.level === 'critical').length,
        warning: data.alerts.filter(a => a.level === 'warning').length
      },
      period: data.period,
      summary: data.summary
    };
  },

  /**
   * 清理告警记录
   */
  onClearAlerts() {
    wx.showModal({
      title: '确认清理',
      content: '确定要清理所有告警记录吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            const productionMonitor = app.globalData.productionMonitor;
            if (productionMonitor) {
              productionMonitor.clearAlerts();
              this.loadMonitoringData();
              wx.showToast({
                title: '清理完成',
                icon: 'success'
              });
            }
          } catch (error) {
            wx.showToast({
              title: '清理失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadMonitoringData().finally(() => {
      wx.stopPullDownRefresh();
    });
  }
});
